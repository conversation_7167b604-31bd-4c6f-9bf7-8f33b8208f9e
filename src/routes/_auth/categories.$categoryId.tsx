import ErrorMessage from "~/components/blocks/error-message";
import PageHeader from "~/components/blocks/page-header";
import SubHeader from "~/components/blocks/sub-header";
import LoadingIndicator from "~/components/elements/loading-indicator";
import CategoryDetailsOverview from "~/features/categories/components/category-details-overview";
import CategoryDialog from "~/features/categories/components/category-dialog";
import { useCategory } from "~/features/categories/hooks";
import TransactionsView from "~/features/transactions/components/transactions-view";

export const Route = createFileRoute({
  component: RouteComponent,
});

function RouteComponent() {
  const { categoryId } = Route.useParams();

  const { data: category, isLoading, error } = useCategory(categoryId);

  if (isLoading) {
    return (
      <>
        <PageHeader title="Category details" backLink={{ to: "/categories" }} />
        <LoadingIndicator />
      </>
    );
  }

  if (error) {
    return (
      <>
        <PageHeader title="Category details" backLink={{ to: "/categories" }} />
        <ErrorMessage title="Can't load category" error={error} />
      </>
    );
  }

  if (!category) {
    return (
      <>
        <PageHeader title="Category details" backLink={{ to: "/categories" }} />
        <ErrorMessage title="Category not found" />
      </>
    );
  }

  return (
    <>
      <PageHeader title="Category details" backLink={{ to: "/categories" }} />

      <div className="space-y-8">
        <CategoryDetailsOverview category={category} />

        <div className="space-y-2">
          <SubHeader title="Transactions" />
          <TransactionsView filters={{ category_id: categoryId }} />
        </div>
      </div>

      <CategoryDialog />
    </>
  );
}
