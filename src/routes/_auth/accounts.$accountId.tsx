import ErrorMessage from "~/components/blocks/error-message";
import PageHeader from "~/components/blocks/page-header";
import SubHeader from "~/components/blocks/sub-header";
import LoadingIndicator from "~/components/elements/loading-indicator";
import AccountDetailsOverview from "~/features/accounts/components/account-details-overview";
import AccountDialog from "~/features/accounts/components/account-dialog";
import { useAccount } from "~/features/accounts/hooks";
import TransactionsView from "~/features/transactions/components/transactions-view";

export const Route = createFileRoute({
  component: RouteComponent,
});

function RouteComponent() {
  const { accountId } = Route.useParams();

  const { data: account, isLoading, error } = useAccount(accountId);

  if (isLoading) {
    return (
      <>
        <PageHeader title="Account details" backLink={{ to: "/accounts" }} />
        <LoadingIndicator />
      </>
    );
  }

  if (error) {
    return (
      <>
        <PageHeader title="Account details" backLink={{ to: "/accounts" }} />
        <ErrorMessage title="Can't load account" error={error} />
      </>
    );
  }

  if (!account) {
    return (
      <>
        <PageHeader title="Account details" backLink={{ to: "/accounts" }} />
        <ErrorMessage title="Account not found" />
      </>
    );
  }

  return (
    <>
      <PageHeader title="Account details" backLink={{ to: "/accounts" }} />

      <div className="space-y-8">
        <AccountDetailsOverview account={account} />

        <div className="space-y-2">
          <SubHeader title="Transactions" />
          <TransactionsView filters={{ account_id: accountId }} />
        </div>
      </div>

      <AccountDialog />
    </>
  );
}
