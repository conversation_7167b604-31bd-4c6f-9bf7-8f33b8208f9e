import { useState } from "react";

import { ChevronDownIcon, ChevronUpIcon, ListFilterIcon as FilterIcon, PlusIcon } from "lucide-react";

import PageHeader from "~/components/blocks/page-header";
import { Button } from "~/components/ui/button";
import TransactionDialog from "~/features/transactions/components/transaction-dialog";
import TransactionFilters from "~/features/transactions/components/transaction-filters";
import TransactionsView from "~/features/transactions/components/transactions-view";
import { useTransactionActions } from "~/features/transactions/hooks";

export const Route = createFileRoute({
  component: RouteComponent,
});

function RouteComponent() {
  const { createTransaction } = useTransactionActions();

  const [filtersOpen, setFiltersOpen] = useState(false);

  return (
    <>
      <PageHeader title="Transactions">
        <div className="flex flex-col gap-4">
          <div className="flex items-center justify-between gap-4">
            <div className="flex-1">
              <button
                onClick={() => setFiltersOpen((open) => !open)}
                className="flex cursor-pointer items-center gap-2 rounded-md px-2 py-1 hover:bg-gray-100"
              >
                <FilterIcon className="size-4 text-gray-700" />
                <span className="text-foreground inline-block pt-0.5 text-xs/5 font-semibold uppercase">Filters</span>
                {filtersOpen ? (
                  <ChevronUpIcon className="text-primary size-4" />
                ) : (
                  <ChevronDownIcon className="text-primary size-4" />
                )}
              </button>
            </div>

            <div>
              <Button onClick={() => createTransaction()}>
                <PlusIcon />
                <span className="inline-block pt-0.5">Add transaction</span>
              </Button>
            </div>
          </div>

          <TransactionFilters open={filtersOpen} />
        </div>
      </PageHeader>

      <TransactionsView />

      <TransactionDialog />
    </>
  );
}
