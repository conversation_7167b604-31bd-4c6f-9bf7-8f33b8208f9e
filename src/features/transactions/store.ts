import type { Transaction, TransactionData, TransactionFilterQuery } from "./types";

import { create } from "zustand";
import { immer } from "zustand/middleware/immer";

type DialogMode = "create" | "edit" | "delete";

interface State {
  // Dialog state
  dialogOpen: boolean;
  dialogMode: DialogMode;
  currentTransaction?: Transaction;
  initialValues?: Partial<TransactionData>;

  // Filtering and pagination state
  filters: TransactionFilterQuery;
  page: number;
}

interface Actions {
  // Dialog actions
  setDialogOpen: (dialogOpen: boolean) => void;
  setDialogMode: (dialogMode: DialogMode) => void;
  setCurrentTransaction: (transaction?: Transaction) => void;
  setInitialValues: (initialValues?: Partial<TransactionData>) => void;
  closeDialog: () => void;

  // Filter and pagination actions
  setFilters: (filters: TransactionFilterQuery) => void;
  setPage: (page: number) => void;
  resetPage: () => void;
}

const useTransactionsStore = create<State & Actions>()(
  immer((set) => ({
    // Dialog state
    dialogOpen: false,
    dialogMode: "create",

    // Filtering and pagination state
    filters: {},
    page: 1,

    // Dialog actions
    setDialogOpen: (dialogOpen) => {
      set({ dialogOpen });
    },
    setDialogMode: (dialogMode) => {
      set({ dialogMode });
    },
    setCurrentTransaction: (transaction) => {
      set({ currentTransaction: transaction });
    },
    setInitialValues: (initialValues) => {
      set({ initialValues });
    },
    closeDialog: () => {
      set({ dialogOpen: false, dialogMode: "create", currentTransaction: undefined, initialValues: undefined });
    },

    // Filter and pagination actions
    setFilters: (filters) => {
      set({ filters, page: 1 }); // Reset to page 1 when filters change
    },
    setPage: (page) => {
      set({ page });
    },
    resetPage: () => {
      set({ page: 1 });
    },
  }))
);

export default useTransactionsStore;
