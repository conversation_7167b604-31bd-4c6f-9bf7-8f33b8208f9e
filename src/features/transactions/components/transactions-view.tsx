import type { TransactionFilterQuery } from "../types";

import { useCallback } from "react";

import { useShallow } from "zustand/react/shallow";

import PaginationBlock from "~/components/blocks/pagination-block";
import LoadingIndicator from "~/components/elements/loading-indicator";

import { useTransactions } from "../hooks";
import useTransactionsStore from "../store";
import TransactionsList from "./transactions-list";

interface Props {
  filters?: TransactionFilterQuery;
}

export default function TransactionsView({ filters: filtersOverride }: Props) {
  const { filters, page, setPage } = useTransactionsStore(
    useShallow((state) => ({
      filters: state.filters,
      page: state.page,
      setPage: state.setPage,
    }))
  );

  const { transactions, pagination, isLoading } = useTransactions({
    page,
    ...(filtersOverride ? filtersOverride : filters),
  });

  const handlePageChange = useCallback(
    (newPage: number) => {
      if (newPage >= 1 && (!pagination || newPage <= pagination.pages)) {
        setPage(newPage);
      }
    },
    [pagination, setPage]
  );

  if (isLoading) {
    return <LoadingIndicator />;
  }

  return (
    <div className="space-y-4">
      <TransactionsList transactions={transactions} />

      {pagination && <PaginationBlock pagination={pagination} currentPage={page} onPageChange={handlePageChange} />}
    </div>
  );
}
