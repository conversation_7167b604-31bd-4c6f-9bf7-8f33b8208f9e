import { useMemo } from "react";

import { useShallow } from "zustand/react/shallow";

import { MultiSelect } from "~/components/ui/multi-select";
import { useAccounts } from "~/features/accounts/hooks";
import { getAccountName } from "~/features/accounts/utils";
import { useCategories } from "~/features/categories/hooks";

import useTransactionsStore from "../store";

// Transaction type options
const transactionTypeOptions = [
  { label: "Income", value: "income" },
  { label: "Expense", value: "expense" },
  { label: "Transfer", value: "transfer" },
];

interface Props {
  open: boolean;
}

export default function TransactionFilters({ open }: Props) {
  const { filters, setFilters } = useTransactionsStore(
    useShallow((state) => ({
      filters: state.filters,
      setFilters: state.setFilters,
    }))
  );
  const { activeAccounts, isLoading: isLoadingAccounts } = useAccounts();
  const { categories, isLoading: isLoadingCategories } = useCategories();

  // Transform accounts to multi-select options
  const accountOptions = useMemo(
    () =>
      activeAccounts.map((account) => ({
        label: getAccountName(account),
        value: account.id,
      })),
    [activeAccounts]
  );

  // Transform categories to multi-select options
  const categoryOptions = useMemo(
    () =>
      categories.map((category) => ({
        label: category.name,
        value: category.id,
      })),
    [categories]
  );

  // Parse current filter values
  const selectedAccountIds = useMemo(
    () => (filters.account_id ? filters.account_id.split(",") : []),
    [filters.account_id]
  );

  const selectedCategoryIds = useMemo(
    () => (filters.category_id ? filters.category_id.split(",") : []),
    [filters.category_id]
  );

  const selectedTransactionTypes = useMemo(
    () => (filters.transaction_type ? filters.transaction_type.split(",") : []),
    [filters.transaction_type]
  );

  // Handle filter changes
  const handleAccountsChange = (accountIds: string[]) => {
    setFilters({
      ...filters,
      account_id: accountIds.length > 0 ? accountIds.join(",") : undefined,
    });
  };

  const handleCategoriesChange = (categoryIds: string[]) => {
    setFilters({
      ...filters,
      category_id: categoryIds.length > 0 ? categoryIds.join(",") : undefined,
    });
  };

  const handleTransactionTypesChange = (types: string[]) => {
    setFilters({
      ...filters,
      transaction_type: types.length > 0 ? types.join(",") : undefined,
    });
  };

  if (!open) {
    return null;
  }

  return (
    <div>
      <div className="flex flex-col gap-4 md:flex-row md:flex-wrap">
        <div className="flex-2">
          <MultiSelect
            key={`accounts-${selectedAccountIds.join(",")}`}
            options={accountOptions}
            onValueChange={handleAccountsChange}
            defaultValue={selectedAccountIds}
            placeholder="Filter by account"
            disabled={isLoadingAccounts}
            maxCount={1}
            modalPopover
          />
        </div>

        <div className="flex-2">
          <MultiSelect
            key={`categories-${selectedCategoryIds.join(",")}`}
            options={categoryOptions}
            onValueChange={handleCategoriesChange}
            defaultValue={selectedCategoryIds}
            placeholder="Filter by category"
            disabled={isLoadingCategories}
            maxCount={1}
            modalPopover
          />
        </div>

        <div className="flex-1">
          <MultiSelect
            key={`types-${selectedTransactionTypes.join(",")}`}
            options={transactionTypeOptions}
            onValueChange={handleTransactionTypesChange}
            defaultValue={selectedTransactionTypes}
            placeholder="Filter by type"
            maxCount={1}
            modalPopover
          />
        </div>
      </div>
    </div>
  );
}
